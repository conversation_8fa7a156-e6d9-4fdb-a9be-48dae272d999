package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.exception.ErrorMsgException;
import com.misyn.mcms.claim.exception.MisynAppException;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.exception.WrongValueException;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.CallCenterServiceImpl;
import com.misyn.mcms.claim.service.impl.RequestAriServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.ListBoxItem;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.ConvertUtilsBean;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "CalculationSheetController", urlPatterns = "/CalculationSheetController/*")
public class CalculationSheetController extends BaseController {


    private static final Logger LOGGER = LoggerFactory.getLogger(CalculationSheetController.class);
    private CalculationSheetService calculationSheetService;
    private ClaimHandlerService claimHandlerService;
    private MotorEngineerService motorEngineerService;
    private RequestAriService requestAriService = new RequestAriServiceImpl();
    private CallCenterService callCenterService = new CallCenterServiceImpl();
    private int draw = 1;


    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        calculationSheetService = getCalculationSheetServiceBySession(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        motorEngineerService = getMotorEngineerBySession(request);
        if (null == requestAriService) {
            requestAriService = new RequestAriServiceImpl();
        }
        ClaimUserTypeDto claimUserTypeDto;
        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/viewCalculationSheet":
                    viewCalculationSheet(request, response);
                    break;
                case "/viewPreviousCalculationSheet":
                    viewPreviousCalculationSheet(request, response);
                    break;
                case "/saveCalculationSheet":
                    saveCalculationSheet(request, response);
                    break;
                case "/loadPaymentOptionPage":
                    loadPaymentOptionPage(request, response);
                    break;
                case "/forwardToSparePartCordinator":
                    forwardToSparePartCordinator(request, response);
                    break;
                case "/forwardToScrutinizingTeam":
                    forwardToScrutinizingTeam(request, response);
                    break;
                case "/forwardToSpecialTeam":
                    forwardToSpecialTeam(request, response);
                    break;
                case "/forwardToMofa":
                    forwardToMofa(request, response);
                    break;
                case "/returnToClaimHandlerBySpc":
                    returnToClaimHandlerBySpc(request, response);
                    break;
                case "/returnToClaimHandlerByStm":
                    returnToClaimHandlerByStm(request, response);
                    break;
                case "/approvePayment":
                    approvePayment(request, response);
                    break;
                case "/callNoObjection":
                    callNoObjection(request, response);
                    break;
                case "/callPremiumOutstanding":
                    callPremiumOutstanding(request, response);
                    break;
                case "/callNoClaimBonus":
                    callNoClaimBonus(request, response);
                    break;
                case "/rejectPayment":
                    rejectPayment(request, response);
                    break;
                case "/calSheetList":
                    calList(request, response);
                    break;
                case "/claimCalList":
                    claimCalList(request, response);
                    break;
                case "/calculate":
                    calculate(request, response);
                    break;
                case "/getMofaUserList":
                    getMofaUserList(request, response);
                    break;
                case "/loadBtnPanel":
                    loadBtnPanel(request, response);
                    break;
                case "/loadBtnPanelNew":
                    loadBtnPanelNew(request, response);
                    break;
                case "/generateVoucher":
                    generateVoucher(request, response);
                    break;
                case "/returnToSparePartCoordinatorByStm":
                    returnToSparePartCoordinatorByStm(request, response);
                    break;
                case "/forwardToScrutinizingTeamByClaimHandler":
                    forwardToScrutinizingTeamByClaimHandler(request, response);
                    break;
                case "/returnToClaimHandlerBySpecialTeam":
                    returnToClaimHandlerBySpecialTeam(request, response);
                    break;
                case "/returnToClaimHandlerAfterApproved":
                    returnToClaimHandlerAfterApproved(request, response);
                    break;
                case "/returnToClaimHandlerByMofa":
                    returnToClaimHandlerByMofa(request, response);
                    break;
                case "/recallByClaimHandler":
                    recallByClaimHandler(request, response);
                    break;
                case "/checkPaybleAmountAndAcrAmount":
                    checkPaybleAmountAndAcrAmount(request, response);
                    break;
                case "/specialRemark":
                    Integer calSheetId = null == request.getParameter("calsheetId") || "".equals(request.getParameter("calsheetId")) ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("calsheetId"));
                    request.setAttribute("calSheetId", calSheetId);
                    request.setAttribute("remarkList", calculationSheetService.calSheetRemarkList(calSheetId));
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calSheetSpecialRemark.jsp");
                    break;
                case "/saveSpecialRemark":
                    saveSpecialRemark(request, response);
                    break;
                case "/checkPayableAmountAndAdvancedAmount":
                    checkPaybleAmountAndAdvancedAmount(request, response);
                    break;
                case "/supplierdetails":
                    getSupplierDetails(request, response);
                    break;
                case "/claimDocumentStatusDto":
                    isCheckedAllLiabilityApproveDocumentForRelease(request, response);
                    break;
                case "/viewPreviousSuperCalculationSheet":
                    viewPreviousSuperCalculationSheet(request, response);
                    break;
                case "/calculateReplacement":
                    calculateReplacement(request, response);
                    break;
                case "/calculateLabour":
                    calculateLabour(request, response);
                    break;
                case "/loadPayeeList":
                    loadPayeeList(request, response);
                    break;
                case "/loadPayeeTypeList":
                    loadPayeeTypeList(request, response);
                    break;
                case "/revokeNoObjection":
                    revokeNoObjection(request, response);
                    break;
                case "/revokePremiumOutstanding":
                    revokePremiumOutstanding(request, response);
                    break;
                case "/recallCalsheetforwardedtoSpTeamByClaimHandler":
                    recallCalsheetforwardedtoSpTeamByClaimHandler(request, response);
                    break;
                case "/recommendAndForwardToNextLevel":
                    recommendAndForwardToNextLevel(request, response);
                    break;
                case "/approveIncreaseReserveByRte":
                    approveIncreaseReserveByRte(request, response);
                    break;
                case "/returnToClaimHandlerByRte":
                    returnToClaimHandlerByRte(request, response);
                    break;
                case "/getPayeeHistoryList":
                    getPayeeHistoryList(request, response);
                    break;
                case "/getMofaHistoryList":
                    getMofaHistoryList(request, response);
                    break;
                case "/forwardToRte":
                    forwardToRte(request, response);
                    break;
                case "/updateBillCheck":
                    updateBillCheck(request, response);
                    break;
                case "/validateDO":
                    validateDO(request, response);
                    break;
                case "/checkPendingPayments":
                    checkPendingPayments(request, response);
                    break;
                case "/checkDuplicateAvailable":
                    checkDuplicateAvailableWhenSave(request, response);
                    break;
                case "/getMofaApprovalUsers":
                    getMofaApprovalUsers(request, response);
                    break;
                case "/forwardToMofaApproval":
                    forwardToMofaApproval(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void checkPendingPayments(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
            boolean isPending = calculationSheetService.checkPendingPayments(claimNo);
            if (isPending) {
                json = gson.toJson(AppConstant.YES);
            } else {
                json = gson.toJson(AppConstant.NO);
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void checkDuplicateAvailableWhenSave(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer calSheetType = null == request.getParameter("calSheetType") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("calSheetType"));
        String json;
        Gson gson = new Gson();
        boolean isDuplicateCalSheetAvailable = false;
        try {
            List<ClaimCalculationSheetMainDto> calculationSheetList = calculationSheetService.getCalculationSheetList(claimNo);
            if(null != calculationSheetList && !calculationSheetList.isEmpty()) {
                for (ClaimCalculationSheetMainDto calculationSheetPrevious : calculationSheetList){
                    if (calculationSheetPrevious != null) {
                        // Check for the duplicate condition based on calSheetType and status
                        if (calSheetType == AppConstant.CAL_SHEET_TYPE_FULL_FINAL ||
                                calSheetType == AppConstant.CAL_SHEET_TYPE_RELEASE_ORDER) {

                            if (calculationSheetPrevious.getCalSheetType() == calSheetType &&
                                    (calculationSheetPrevious.getStatus() != AppConstant.CAL_SHEET_VOUCHER_GENERATE &&
                                            calculationSheetPrevious.getStatus() != AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING)) {

                                isDuplicateCalSheetAvailable = true;
                                break;  // Exit the loop once a duplicate is found

                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            json = gson.toJson(isDuplicateCalSheetAvailable);
            printWriter(request, response, json);
        }
    }

    private void validateDO(HttpServletRequest request, HttpServletResponse response) {
        Integer calsheetId = null == request.getParameter("N_CALSHEET_ID") || request.getParameter("N_CALSHEET_ID").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CALSHEET_ID"));
        String json;
        Gson gson = new Gson();
        try {
            Boolean isValid = calculationSheetService.validateDO(calsheetId);
            json = gson.toJson(isValid ? "VALID" : "INVALID");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void updateBillCheck(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        NotificationMsgDto notificationMsgDto = new NotificationMsgDto();
        try {
            UserDto user = getSessionUser(request);
            Integer id = Integer.parseInt(request.getParameter("id"));
            String isChecked = request.getParameter("isChecked");
            calculationSheetService.updateBillCheck(id, isChecked, user);

            notificationMsgDto.setCode(AppConstant.SUCCESS);
            if (AppConstant.YES.equalsIgnoreCase(isChecked)) {
                notificationMsgDto.setMsg(" Successfully bill checked");
            } else {
                notificationMsgDto.setMsg(" Successfully bill unchecked");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            notificationMsgDto.setCode(AppConstant.SUCCESS);
            notificationMsgDto.setMsg("bill check failed");
        }
        printWriter(request, response, gson.toJson(notificationMsgDto));
    }

    private void forwardToRte(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        BigDecimal payableAmount = null == request.getParameter("PAYABLE") || request.getParameter("PAYABLE").isEmpty() ? BigDecimal.ZERO : new BigDecimal(request.getParameter("PAYABLE"));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.forwardToRte(calSheetId, claimNo, payableAmount, user, outStandingPremium);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(null == e.getMessage() ? AppConstant.STRING_EMPTY : e.getMessage());
            printWriter(request, response, json);
        }
    }

    private void getMofaHistoryList(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer calSheetId = null == request.getParameter("calsheetId") ? 0 : Integer.parseInt(request.getParameter("calsheetId"));
            Gson gson = new Gson();
            List<CalculationSheetMofaLevelHistoryDto> list = calculationSheetService.getMofaHistoryDetails(calSheetId);
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    private void getPayeeHistoryList(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer calSheetId = null == request.getParameter("calsheetId") ? 0 : Integer.parseInt(request.getParameter("calsheetId"));
            Integer claimNo = null == request.getParameter("claimNo") ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            Gson gson = new Gson();
            List<CalculationSheetPayeeHistoryDto> list = calculationSheetService.getPayeeHistoryDetails(calSheetId, claimNo);
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    private void returnToClaimHandlerByRte(HttpServletRequest request, HttpServletResponse response) {
        int errorCode;
        Integer calSheetId = null == request.getParameter("P_CAL_SHEET_ID") ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_ID"));
        Integer claimNo = null == request.getParameter("P_CLAIM_NO") ? 0 : Integer.parseInt(request.getParameter("P_CLAIM_NO"));
        String remark = null == request.getParameter("rteRemark") ? AppConstant.STRING_EMPTY : request.getParameter("rteRemark");
        UserDto user = getSessionUser(request);

        try {
            try {
                errorCode = 5;
                calculationSheetService.returnToClaimHandlerByRte(claimNo, calSheetId, remark, user);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                errorCode = 6;
            }
            request.getSession().setAttribute("errorCode", String.valueOf(errorCode));
            response.sendRedirect(request.getContextPath() + "/CalculationSheetController" +
                    "/viewPreviousCalculationSheet?calSheetId=" + calSheetId
                    + "&claimNo=" + claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void approveIncreaseReserveByRte(HttpServletRequest request, HttpServletResponse response) {
        int errorCode = 8;
        Integer calSheetId = null == request.getParameter("P_CAL_SHEET_ID") ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_ID"));
        Integer claimNo = null == request.getParameter("P_CLAIM_NO") ? 0 : Integer.parseInt(request.getParameter("P_CLAIM_NO"));
        String remark = null == request.getParameter("rteRemark") ? AppConstant.STRING_EMPTY : request.getParameter("rteRemark");
        UserDto user = getSessionUser(request);

        try {
            try {
                calculationSheetService.approveIncreaseReserveByRte(claimNo, calSheetId, remark, user);
            } catch (MisynAppException e) {
                if ("301".equalsIgnoreCase(e.getErrorCode())) {
                    errorCode = 301;
                } else if ("302".equalsIgnoreCase(e.getErrorCode())) {
                    errorCode = 302;
                } else if ("303".equalsIgnoreCase(e.getErrorCode())) {
                    errorCode = 303;
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                errorCode = 3;
            }
            request.getSession().setAttribute("errorCode", String.valueOf(errorCode));
            response.sendRedirect(request.getContextPath() + "/CalculationSheetController" +
                    "/viewPreviousCalculationSheet?calSheetId=" + calSheetId
                    + "&claimNo=" + claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void recommendAndForwardToNextLevel(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter(AppConstant.CAL_SHEET_ID) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.CAL_SHEET_ID));
        Integer claimNo = request.getParameter(AppConstant.CLAIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.CLAIM_NO));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        boolean isCancelledPolicy = null != request.getParameter("IS_CANCELLED_POLICY") && Boolean.parseBoolean(request.getParameter("IS_CANCELLED_POLICY"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.recommendAndForwardToNextLevel(calSheetId, claimNo, user, outStandingPremium, isCancelledPolicy);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (ErrorMsgException ex) {
            json = AppConstant.STRING_EMPTY;
            if (AppConstant.EXCESS_RESERVE_ERROR_MSG.equals(ex.getField())) {
                json = gson.toJson("EXCESS_RESERVE");
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void callNoClaimBonus(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String email = request.getParameter("email") == null ? AppConstant.STRING_EMPTY : request.getParameter("email");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.callNoClaimBonus(calSheetId, claimNo, email, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void viewCalculationSheet(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        boolean ariArranged = false;
        try {
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));

            if (0 == claimNo) {
                claimNo = (Integer) request.getAttribute("ClaimNo");
            }

            List<ClaimCalculationSheetMainDto> advanceListForClaim = calculationSheetService.getAdvanceListForClaim(claimNo);
            BigDecimal totalAmountPaidForClaim = calculationSheetService.getTotalPaidForClaim(claimNo);
            BigDecimal totalPaidAdvanceAmountForClaim = calculationSheetService.getTotalPaidAdvanceForClaim(claimNo);
            int salvageORAriArranged = requestAriService.isSalvageORAriArranged(claimNo);

            if (salvageORAriArranged == AppConstant.ARI_INSPECTION || salvageORAriArranged == AppConstant.SALVAGE_INSPECTION) {
                ariArranged = true;
            }

            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            calculationSheetService.setOtherDetailsList(claimHandlerDto.getClaimsDto());
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(claimNo, 0);
            if (claimCalculationSheetMainDto != null) {
                claimCalculationSheetMainDto.setAssignUserId(user.getUserId());
                claimCalculationSheetMainDto.setStatus(0);
                json = gson.toJson(claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDtos());
                claimCalculationSheetMainDto.setPayeeDtosJson(json);

                json = gson.toJson(claimCalculationSheetMainDto.getBranchDetailDtos());
                claimCalculationSheetMainDto.setBranchDetailsJson(json);
            }

            List<ClaimCalculationSheetMainDto> calculationSheetList = calculationSheetService.getCalculationSheetList(claimNo);

            String isExcessPaid = "N";
            String isExcessReject = "N";

            for (ClaimCalculationSheetMainDto calculationSheetMainDto : calculationSheetList) {
                if ("Y".equals(calculationSheetMainDto.getIsExcessInclude()) && calculationSheetMainDto.getStatus() == 65) { //65 -> Payment Approved
                    isExcessPaid = "Y";
                    isExcessReject = "N";
                    break;
                } else if ("Y".equals(calculationSheetMainDto.getIsExcessInclude()) && calculationSheetMainDto.getStatus() != 66 && calculationSheetMainDto.getStatus() != 73) { //66 -> Payment Rejected/ 73 -> Payment Cancelled
                    if (calculationSheetMainDto.getCalSheetId() != 0) {
                        isExcessPaid = "Y";
                        isExcessReject = "N";
                        break;
                    }
                } else if ("Y".equals(calculationSheetMainDto.getIsExcessInclude()) && (calculationSheetMainDto.getStatus() == 66 || calculationSheetMainDto.getStatus() == 73)) { //66 -> Payment Rejected
                    isExcessPaid = "Y";
                    isExcessReject = "Y";
                }
            }

            if (AppConstant.REOPEN_TYPE_EX_GRATIA.equals(claimHandlerDto.getReOpenType())) {
                request.setAttribute("IS_EX_GRATIA", "Y");
            } else {
                request.setAttribute("IS_EX_GRATIA", "N");
            }
            if (AppConstant.YES.equals(claimHandlerDto.getIsExcessInclude())) {
                isExcessPaid = AppConstant.YES;
                claimCalculationSheetMainDto.setIsExcessInclude(isExcessPaid);
            }

            request.setAttribute(AppConstant.ARI_ARRANGED, ariArranged);
            request.setAttribute("IS_EXCESS_PAID", isExcessPaid);
            request.setAttribute("IS_EXCESS_REJECT", isExcessReject);
            request.setAttribute("claimCalculationSheetMainDto", claimCalculationSheetMainDto);
            request.setAttribute("IS_EXCESS_ALREADY_APPLY", isExcessPaid);
            request.setAttribute("claimHandlerDto", claimHandlerDto);
            request.getSession().setAttribute("claimHandlerDto", claimHandlerDto);
            request.setAttribute("totalAmountPaidForClaim", totalAmountPaidForClaim);
            request.setAttribute("totalPaidAdvanceAmountForClaim", totalPaidAdvanceAmountForClaim);
            request.setAttribute("advanceListForClaim", advanceListForClaim);
            request.setAttribute("isAlreadySavedCalsheet", false);
            request.setAttribute("ACTION", "SAVE");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        //   requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calculationSheet.jsp");
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calculationSheet/calculationSheetNew.jsp");
    }

    private void isCheckedAllLiabilityApproveDocumentForRelease(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer calSheetType = request.getParameter("calSheetType") == null ? 0 : Integer.parseInt(request.getParameter("calSheetType"));
        String json;
        Gson gson = new Gson();
        ClaimDocumentStatusDto claimDocumentStatusDto = new ClaimDocumentStatusDto();
        try {
            if (calSheetType == 3) {
                claimDocumentStatusDto = this.getClaimDocumentStatusDto(claimNo, String.valueOf(AppConstant.DO_BILL_DOCUMENT_TYPE_ID));
            } else if (calSheetType == 5) {
                claimDocumentStatusDto = this.getClaimDocumentStatusDto(claimNo, String.valueOf(AppConstant.GARAGE_BILL_DOCUMENT_TYPE_ID));
            }
            json = gson.toJson(claimDocumentStatusDto.getDocumentStatusEnum().getDocumentStatus());
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("P");
            printWriter(request, response, json);
        }
    }

    private ClaimDocumentStatusDto getClaimDocumentStatusDto(Integer claimNo, String constType) throws Exception {
        String[] typeArr = constType.split(",");
        ClaimDocumentStatusDto claimDocumentStatusDto = null;
        for (String type : typeArr) {
            if (null == claimDocumentStatusDto) {
                claimDocumentStatusDto = calculationSheetService.getClaimDocumentStatus(claimNo, Integer.parseInt(type));
            } else {
                claimDocumentStatusDto.getClaimDocumentDtoList()
                        .addAll(calculationSheetService.getClaimDocumentStatus(claimNo, Integer.parseInt(type)).getClaimDocumentDtoList());
            }
        }
        return claimDocumentStatusDto;
    }

    private void viewPreviousCalculationSheet(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        UserDto user = getSessionUser(request);
        boolean isEngDocUpload = null != request.getParameter("DOC_UPLOAD") && !request.getParameter("DOC_UPLOAD").isEmpty() && Boolean.parseBoolean(request.getParameter("DOC_UPLOAD"));
        boolean ariArranged = false;
        try {
            String code = (String) request.getSession().getAttribute("errorCode");
            request.getSession().removeAttribute("errorCode");
            Integer errorCode = code == null ? 0 : Integer.parseInt(code);
            Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            if (0 == calSheetId) {
                calSheetId = (Integer) request.getAttribute("calSheetId");
            }
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(claimNo, calSheetId);
            if (claimCalculationSheetMainDto != null) {
                json = gson.toJson(claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDtos());
                claimCalculationSheetMainDto.setPayeeDtosJson(json);

                json = gson.toJson(claimCalculationSheetMainDto.getBranchDetailDtos());
                claimCalculationSheetMainDto.setBranchDetailsJson(json);
            }


            BigDecimal totalAmountPaidForClaim = calculationSheetService.getTotalPaidForClaim(claimCalculationSheetMainDto.getClaimNo());
            BigDecimal totalPaidAdvanceAmountForClaim = calculationSheetService.getTotalPaidAdvanceForClaim(claimCalculationSheetMainDto.getClaimNo());

            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimCalculationSheetMainDto.getClaimNo());
            calculationSheetService.setOtherDetailsList(claimHandlerDto.getClaimsDto());

            List<ClaimCalculationSheetMainDto> calculationSheetList = calculationSheetService.getCalculationSheetList(claimNo);
            int salvageORAriArranged = requestAriService.isSalvageORAriArranged(claimNo);

            if (salvageORAriArranged == AppConstant.ARI_INSPECTION || salvageORAriArranged == AppConstant.SALVAGE_INSPECTION) {
                ariArranged = true;
            }

            String isExcessPaid = AppConstant.NO;
            String isExcessReject = AppConstant.NO;
            String isExcessAlreadyApply = AppConstant.NO;
            for (ClaimCalculationSheetMainDto calculationSheetMainDto : calculationSheetList) {
                if (AppConstant.YES.equals(calculationSheetMainDto.getIsExcessInclude()) && calculationSheetMainDto.getStatus() == 65) { //65->Payment Approved
                    isExcessPaid = AppConstant.YES;
                    isExcessReject = AppConstant.NO;
                    break;
                } else if (AppConstant.YES.equals(calculationSheetMainDto.getIsExcessInclude()) && calculationSheetMainDto.getStatus() != 66 && calculationSheetMainDto.getStatus() != 73) { //66->Payment Rejected / 73-> Payment Cancelled
                    if (!calculationSheetMainDto.getCalSheetId().equals(calSheetId)) {
                        isExcessPaid = AppConstant.YES;
                        isExcessReject = AppConstant.NO;
                        break;
                    }

                } else if ("Y".equals(calculationSheetMainDto.getIsExcessInclude()) && (calculationSheetMainDto.getStatus() == 66 || calculationSheetMainDto.getStatus() == 73)) { //66->Payment Rejected
                    isExcessPaid = AppConstant.YES;
                    isExcessReject = AppConstant.YES;
                }
            }
            if (AppConstant.YES.equals(claimHandlerDto.getIsExcessInclude())) {
                isExcessPaid = AppConstant.YES;
                claimCalculationSheetMainDto.setIsExcessInclude(isExcessPaid);
            }
            isExcessAlreadyApply = isExcessPaid;

            if (user.getUserId().equalsIgnoreCase(claimCalculationSheetMainDto.getAssignUserId()) && isExcessAlreadyApply.equalsIgnoreCase(AppConstant.YES)) {
                isExcessAlreadyApply = calculationSheetService.isExcessAlreadyApply(claimNo, calSheetId);
            }

            if (AppConstant.REOPEN_TYPE_EX_GRATIA.equals(claimHandlerDto.getReOpenType())) {
                request.setAttribute("IS_EX_GRATIA", AppConstant.YES);
            } else {
                request.setAttribute("IS_EX_GRATIA", AppConstant.NO);
            }

            ClaimCalculationSheetMainTempDto tempDto = calculationSheetService.getCalsheetTempDetails(calSheetId);

            boolean isAlreadySavedCalsheet = false;
            if (null != tempDto) {
                isAlreadySavedCalsheet = true;
            }
            request.setAttribute("ariArranged", ariArranged);
            request.setAttribute("G_USER", user);
            request.setAttribute("isAlreadySavedCalsheet", isAlreadySavedCalsheet);
            request.setAttribute(AppConstant.TEMP_DTO, tempDto);
            request.setAttribute("IS_EXCESS_PAID", isExcessPaid);
            request.setAttribute("IS_EXCESS_REJECT", isExcessReject);
            request.setAttribute("IS_EXCESS_ALREADY_APPLY", isExcessAlreadyApply);
            request.setAttribute("claimHandlerDto", claimHandlerDto);
            request.getSession().setAttribute("claimHandlerDto", claimHandlerDto);
            request.setAttribute("ACTION", "UPDATE");
            request.setAttribute("totalAmountPaidForClaim", totalAmountPaidForClaim);
            request.setAttribute("totalPaidAdvanceAmountForClaim", totalPaidAdvanceAmountForClaim);
            request.setAttribute("claimCalculationSheetMainDto", claimCalculationSheetMainDto);
            //        request.getSession().setAttribute("claimCalculationSheetMainDto_SESSION", claimCalculationSheetMainDto);

            request.setAttribute("ClaimNo", claimCalculationSheetMainDto.getClaimNo());
            request.setAttribute("calSheetId", claimCalculationSheetMainDto.getCalSheetId());
            request.setAttribute(AppConstant.DOC_UPLOAD, isEngDocUpload);

            switch (errorCode) {
                case 1:
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Updated");
                    break;
                case 2:
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Saved");
                    break;
                case 3:
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be updated");
                    break;
                case 4:
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be saved");
                    break;
                case 5:
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Returned Successfully");
                    break;
                case 6:
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Return Failed");
                    break;
                case 7:
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Cal Sheet has been successfully forwarded to RTE for Recommendation");
                    break;
                case 8:
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "You have successfully forwarded the cal sheet with recommendation to special team user");
                    break;
                case 301:
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Please select valid payee - Return Failed");
                    break;
                case 302:
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Please select valid payee amount - Return Failed");
                    break;
                case 303:
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Please select valid payee desc - Return Failed");
                    break;
                case 9:
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "User not found to assign claim");
                    break;
                case 10:
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Saved as Draft");
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        //   requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calculationSheet.jsp");
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calculationSheet/calculationSheetNew.jsp");
    }

    private void viewPreviousSuperCalculationSheet(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        try {
            Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            if (0 == calSheetId) {
                calSheetId = (Integer) request.getAttribute("calSheetId");
            }
            String historyRecord = request.getParameter("history");
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(claimNo, calSheetId);


            if (claimCalculationSheetMainDto != null) {
                json = gson.toJson(claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDtos());
                claimCalculationSheetMainDto.setPayeeDtosJson(json);

                json = gson.toJson(claimCalculationSheetMainDto.getBranchDetailDtos());
                claimCalculationSheetMainDto.setBranchDetailsJson(json);
            }

            List<ClaimCalculationSheetMainDto> advanceListForClaimForFilter = calculationSheetService.getAdvanceListForClaim(claimCalculationSheetMainDto.getClaimNo());
            List<ClaimCalculationSheetMainDto> advanceListForClaim = new ArrayList<>();

            for (ClaimCalculationSheetMainDto dto : advanceListForClaimForFilter) {
                if (!calSheetId.equals(dto.getCalSheetId())) {
                    advanceListForClaim.add(dto);
                }
            }

            BigDecimal totalAmountPaidForClaim = calculationSheetService.getTotalPaidForClaim(claimCalculationSheetMainDto.getClaimNo());
            BigDecimal totalPaidAdvanceAmountForClaim = calculationSheetService.getTotalPaidAdvanceForClaim(claimCalculationSheetMainDto.getClaimNo());

            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimCalculationSheetMainDto.getClaimNo());

            List<ClaimCalculationSheetMainDto> calculationSheetList = calculationSheetService.getCalculationSheetList(claimNo);

            String isExcessPaid = "N";
            String isExcessReject = "N";

            for (ClaimCalculationSheetMainDto calculationSheetMainDto : calculationSheetList) {
                if ("Y".equals(calculationSheetMainDto.getIsExcessInclude()) && calculationSheetMainDto.getStatus() == 65) { //65->Payment Approved
                    isExcessPaid = "Y";
                    isExcessReject = "N";
                    break;
                } else if ("Y".equals(calculationSheetMainDto.getIsExcessInclude()) && calculationSheetMainDto.getStatus() != 66) { //66->Payment Rejected
                    isExcessPaid = "Y";
                    isExcessReject = "N";
                    break;
                } else if ("Y".equals(calculationSheetMainDto.getIsExcessInclude()) && calculationSheetMainDto.getStatus() == 66) { //66->Payment Rejected
                    isExcessPaid = "Y";
                    isExcessReject = "Y";
                }
            }
            if (AppConstant.YES.equals(claimHandlerDto.getIsExcessInclude())) {
                isExcessPaid = AppConstant.YES;
                claimCalculationSheetMainDto.setIsExcessInclude(isExcessPaid);
            }

            if (AppConstant.REOPEN_TYPE_EX_GRATIA.equals(claimHandlerDto.getReOpenType())) {
                request.setAttribute("IS_EX_GRATIA", "Y");
            } else {
                request.setAttribute("IS_EX_GRATIA", "N");
            }

            request.setAttribute("IS_EXCESS_PAID", isExcessPaid);
            request.setAttribute("IS_EXCESS_REJECT", isExcessReject);
            request.setAttribute("claimHandlerDto", claimHandlerDto);
            request.setAttribute("ACTION", "UPDATE");
            request.setAttribute("totalAmountPaidForClaim", totalAmountPaidForClaim);
            request.setAttribute("totalPaidAdvanceAmountForClaim", totalPaidAdvanceAmountForClaim);
            request.setAttribute("claimCalculationSheetMainDto", claimCalculationSheetMainDto);
            //  request.getSession().setAttribute("claimCalculationSheetMainDto_SESSION", claimCalculationSheetMainDto);
            request.getSession().setAttribute("superDashboardClaimHandlerDtoSession", claimHandlerDto);

            request.setAttribute("advanceListForClaim", advanceListForClaim);
            request.setAttribute("HISTORY_RECORD", historyRecord);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calculationSheet.jsp");

    }

    private void saveCalculationSheet(HttpServletRequest request, HttpServletResponse response) {
        try {
            UserDto user = getSessionUser(request);
            Integer errorCode = 0;
            Integer calSheetId;
            Integer claimNo;
            String action = request.getParameter("ACTION");
            String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
            boolean isReserveAmountExceed = null != request.getParameter("IS_RESERVE_AMOUNT_EXCEED") && AppConstant.YES.equals(request.getParameter("IS_RESERVE_AMOUNT_EXCEED")) ? true : false;
            BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
            boolean isDraftSave = null != request.getParameter(AppConstant.ACTION_TYPE) && AppConstant.DRAFT.equalsIgnoreCase(request.getParameter(AppConstant.ACTION_TYPE)) ? true : false;
            boolean isCancelledPolicy = null == request.getParameter("IS_CANCELLED_POLICY") ? false : Boolean.parseBoolean(request.getParameter("IS_CANCELLED_POLICY"));
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto;
            if ("SAVE".equals(action)) {
                claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
            } else {
                calSheetId = request.getParameter("P_CAL_SHEET_ID") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_ID"));
                claimNo = request.getParameter("P_CLAIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CLAIM_NO"));
                claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(claimNo, calSheetId);
                if ("UPDATE".equals(action) && calSheetId == 0) {
                    errorCode = 4;
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be saved");
                    request.getSession().setAttribute("errorCode", String.valueOf(errorCode));
                    response.sendRedirect(request.getContextPath() + "/CalculationSheetController" +
                            "/viewPreviousCalculationSheet?calSheetId=" + calSheetId
                            + "&claimNo=" + claimNo);
                    return;
                }

            }

            //Configure Bean Utils
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.getConvertUtils().register(false, false, 0);
            Map<String, String[]> parameterMap = request.getParameterMap();
            Enumeration<String> parameterNames = request.getParameterNames();

            populateDtoFromMap(claimCalculationSheetMainDto, parameterMap, null);

            List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos = new ArrayList<>();
            List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos = new ArrayList<>();
            List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos = new ArrayList<>();

            List<String> processedRepalcementIndexList = new ArrayList<>();
            List<String> processedLabourIndexList = new ArrayList<>();
            List<String> processedPayeeIndexList = new ArrayList<>();

            while (parameterNames.hasMoreElements()) {
                String parameterName = parameterNames.nextElement();
                if (parameterName.contains("claimCalculationSheetDetailReplacementDtos")) {
                    String[] parameterNameArr = parameterName.split("\\.");
                    String parameterNamePrefix = parameterNameArr[0];
                    String parameterNameIndex = parameterNamePrefix.replaceAll("claimCalculationSheetDetailReplacementDtos\\[", "").replaceAll("\\]", "");
                    if (!processedRepalcementIndexList.contains(parameterNameIndex)) {
                        ClaimCalculationSheetDetailDto claimCalculationSheetDetailRepalcementDto = new ClaimCalculationSheetDetailDto();
                        populateDtoFromMap(claimCalculationSheetDetailRepalcementDto, parameterMap, parameterNamePrefix);
                        processedRepalcementIndexList.add(parameterNameIndex);
                        claimCalculationSheetDetailReplacementDtos.add(claimCalculationSheetDetailRepalcementDto);
                    }
                } else if (parameterName.contains("claimCalculationSheetDetailLabourDtos")) {
                    String[] parameterNameArr = parameterName.split("\\.");
                    String parameterNamePrefix = parameterNameArr[0];
                    String parameterNameIndex = parameterNamePrefix.replaceAll("claimCalculationSheetDetailLabourDtos\\[", "").replaceAll("\\]", "");
                    if (!processedLabourIndexList.contains(parameterNameIndex)) {
                        ClaimCalculationSheetDetailDto claimCalculationSheetDetailLabourDto = new ClaimCalculationSheetDetailDto();
                        populateDtoFromMap(claimCalculationSheetDetailLabourDto, parameterMap, parameterNamePrefix);
                        processedLabourIndexList.add(parameterNameIndex);
                        claimCalculationSheetDetailLabourDtos.add(claimCalculationSheetDetailLabourDto);
                    }
                } else if (parameterName.contains("payeeDtos")) {
                    String[] parameterNameArr = parameterName.split("\\.");
                    String parameterNamePrefix = parameterNameArr[0];
                    String parameterNameIndex = parameterNamePrefix.replaceAll("payeeDtos\\[", "").replaceAll("\\]", "");
                    if (!processedPayeeIndexList.contains(parameterNameIndex)) {
                        ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto = new ClaimCalculationSheetPayeeDto();
                        populateDtoFromMap(claimCalculationSheetPayeeDto, parameterMap, parameterNamePrefix);
                        processedPayeeIndexList.add(parameterNameIndex);
                        if (0 < claimCalculationSheetPayeeDto.getPayeeId()) {
                            BranchDetailDto branchDetailDto = new BranchDetailDto();
                            populateDtoFromMap(branchDetailDto, parameterMap, parameterNamePrefix.concat(".branchDetailDto"));
                            if (null != branchDetailDto.getBranchCode() && !branchDetailDto.getBranchCode().isEmpty()) {
                                claimCalculationSheetPayeeDto.setBranchDetailDto(branchDetailDto);
                            }
                            claimCalculationSheetPayeeDtos.add(claimCalculationSheetPayeeDto);
                        }
                    }
                }

            }

            if ((AppConstant.SAVE.equals(action) && !isDraftSave) ||
                    ((null != claimCalculationSheetMainDto.getStatus() && ClaimStatus.SAVE_AS_DRAFT.getClaimStatus() == claimCalculationSheetMainDto.getStatus()) && !isDraftSave)) {
                claimCalculationSheetMainDto.setStatus(ClaimStatus.CALCULATION_VERIFY_PENDING.getClaimStatus());
                claimCalculationSheetMainDto.setNoObjectionStatus(AppConstant.STRING_PENDING);
                claimCalculationSheetMainDto.setPremiumOutstandingStatus(AppConstant.STRING_PENDING);
                claimCalculationSheetMainDto.setNcbStatus(AppConstant.STRING_PENDING);
            } else if (isDraftSave) {
                claimCalculationSheetMainDto.setStatus(ClaimStatus.SAVE_AS_DRAFT.getClaimStatus());
                claimCalculationSheetMainDto.setNoObjectionStatus(AppConstant.STRING_PENDING);
                claimCalculationSheetMainDto.setPremiumOutstandingStatus(AppConstant.STRING_PENDING);
                claimCalculationSheetMainDto.setNcbStatus(AppConstant.STRING_PENDING);
            }
            if (claimCalculationSheetMainDto.getCalSheetType() == 3) {
                String supplierOrderId = request.getParameter("supplierOrderId");
                ClaimCalculationSheetSupplierOrderDto claimCalculationSheetSupplierOrderDto = new ClaimCalculationSheetSupplierOrderDto();
                claimCalculationSheetSupplierOrderDto.setSupplierOrderId(Integer.parseInt(supplierOrderId));
                claimCalculationSheetMainDto.setClaimCalculationSheetSupplierOrderDto(claimCalculationSheetSupplierOrderDto);
            }

            switch (action) {
                case AppConstant.UPDATE:
                    calSheetId = request.getParameter("P_CAL_SHEET_ID") == null ? 0 : Integer.parseInt(request.getParameter("P_CAL_SHEET_ID"));
                    claimCalculationSheetMainDto.setCalSheetId(calSheetId);
                case AppConstant.SAVE:
                    claimNo = request.getParameter("P_CLAIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_CLAIM_NO"));
                    claimCalculationSheetMainDto.setClaimNo(claimNo);
                    break;
                default:
                    throw new AssertionError("No Action Found");
            }

            claimCalculationSheetMainDto.setClaimCalculationSheetDetailReplacementDtos(claimCalculationSheetDetailReplacementDtos);
            claimCalculationSheetMainDto.setClaimCalculationSheetDetailLabourDtos(claimCalculationSheetDetailLabourDtos);
            claimCalculationSheetMainDto.setClaimCalculationSheetPayeeDtos(claimCalculationSheetPayeeDtos);


            try {
                claimCalculationSheetMainDto.setSpecialRemark(remark);
                calculationSheetService.saveCalculationSheet(action, user, claimCalculationSheetMainDto, outStandingPremium, isReserveAmountExceed, isDraftSave, isCancelledPolicy);
                if (AppConstant.UPDATE.equals(action)) {
                    if (isReserveAmountExceed) {
                        errorCode = 7;
                        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Forwarded");
                    } else if (isDraftSave) {
                        errorCode = 10;
                        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Saved as Draft");
                    } else {
                        errorCode = 1;
                        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Updated");
                    }
                } else {
                    if (isReserveAmountExceed) {
                        errorCode = 7;
                        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Forwarded");
                    } else if (isDraftSave) {
                        errorCode = 10;
                        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Saved as Draft");
                    } else {
                        errorCode = 2;
                        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Saved");
                    }

                }
            } catch (UserNotFoundException e) {
                LOGGER.error(e.getMessage());
                errorCode = 9;
                request.setAttribute(AppConstant.ERROR_MESSAGE, e.getErrorMessage());
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
                if (AppConstant.UPDATE.equals(action)) {
                    errorCode = 3;
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be updated");
                } else {
                    errorCode = 4;
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be saved");
                }
            }

            if (null == claimCalculationSheetMainDto || null == claimCalculationSheetMainDto.getCalSheetId() || null == claimCalculationSheetMainDto.getClaimNo()) {
                claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
                claimCalculationSheetMainDto.setCalSheetId(0);
                claimCalculationSheetMainDto.setClaimNo(0);
            }

            request.getSession().setAttribute("errorCode", String.valueOf(errorCode));
            response.sendRedirect(request.getContextPath() + "/CalculationSheetController" +
                    "/viewPreviousCalculationSheet?calSheetId=" + claimCalculationSheetMainDto.getCalSheetId()
                    + "&claimNo=" + claimCalculationSheetMainDto.getClaimNo());

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    private void checkPaybleAmountAndAcrAmount(HttpServletRequest request, HttpServletResponse response) {
        BigDecimal diff = BigDecimal.ZERO;
        String json;
        Gson gson = new Gson();
        try {
            BigDecimal payableAmount = null == request.getParameter("payableAmount") ? BigDecimal.ZERO : new BigDecimal(request.getParameter("payableAmount"));
            BigDecimal reserveAmount = null == new BigDecimal(request.getParameter("reserveAmount")) ? BigDecimal.ZERO : new BigDecimal(request.getParameter("reserveAmount"));
            BigDecimal totalApprovedAcr = null == new BigDecimal(request.getParameter("totalApprovedAcr")) ? BigDecimal.ZERO : new BigDecimal(request.getParameter("totalApprovedAcr"));
            String rteAction = null == request.getParameter("rteAction") || request.getParameter("rteAction").isEmpty() ? AppConstant.STRING_EMPTY : request.getParameter("rteAction");
            Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
            Integer calSheetId = null == request.getParameter("calSheetId") || request.getParameter("calSheetId").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("calSheetId"));

            BigDecimal totalPaidAmount = calculationSheetService.getTotalPaidAmount(claimNo, calSheetId);
            BigDecimal totalPayableAmount = payableAmount.add(totalPaidAmount);
            BigDecimal engineerAprvAmt = claimHandlerService.getEngineerApprovedAmount(claimNo);

            if (!rteAction.equals(AppConstant.STRING_EMPTY) && (rteAction.equals(AppConstant.APPROVE))) {
                if (totalPayableAmount.compareTo(engineerAprvAmt) > 0) {
                    diff = totalPayableAmount.subtract(engineerAprvAmt);
                }
            } else {
                if (totalPayableAmount.compareTo(totalApprovedAcr) > 0) {
                    diff = totalPayableAmount.subtract(totalApprovedAcr);
                }
            }
            json = gson.toJson(diff);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void checkPaybleAmountAndAdvancedAmount(HttpServletRequest request, HttpServletResponse response) {
        try {
            BigDecimal payableAmount = null == request.getParameter("payableAmount") ? BigDecimal.ZERO : new BigDecimal(request.getParameter("payableAmount"));
            BigDecimal advancedAmount = null == new BigDecimal(request.getParameter("advancedAmount")) ? BigDecimal.ZERO : new BigDecimal(request.getParameter("advancedAmount"));
            BigDecimal diff = BigDecimal.ZERO;
            String json;
            Gson gson = new Gson();

            if (payableAmount.compareTo(advancedAmount) > 0) {
                diff = payableAmount.subtract(advancedAmount);

            }
            json = gson.toJson(diff);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void populateDtoFromMap(Object object, Map<String, String[]> parameterMap, String parameterNamePrefix) throws Exception {
        try {
            Field[] declaredFieldsMain = object.getClass().getDeclaredFields();
            for (Field declaredField : declaredFieldsMain) {
                if (!"serialVersionUID".equals(declaredField.getName())) {
                    String[] parameterValue;
                    if (null != parameterNamePrefix && !parameterNamePrefix.isEmpty()) {
                        if (parameterMap.containsKey(parameterNamePrefix + "." + declaredField.getName())) {
                            parameterValue = parameterMap.get(parameterNamePrefix + "." + declaredField.getName());
                        } else {
                            continue;
                        }
                    } else {
                        if (parameterMap.containsKey(declaredField.getName())) {
                            parameterValue = parameterMap.get(declaredField.getName());
                        } else {
                            continue;
                        }
                    }
                    Object value = null;
                    if (Integer.class.equals(declaredField.getType())) {
                        if (NumberUtils.isNumber(parameterValue[0])) {
                            value = null == parameterValue || null == parameterValue[0] || parameterValue[0].isEmpty() ? 0 : Integer.parseInt(parameterValue[0]);
                        } else {
                            value = 0;
                        }

                    } else if (BigDecimal.class.equals(declaredField.getType())) {
                        if (NumberUtils.isNumber(parameterValue[0])) {
                            value = null == parameterValue || null == parameterValue[0] || parameterValue[0].isEmpty() ? BigDecimal.ZERO : new BigDecimal(parameterValue[0]);
                        } else {
                            value = BigDecimal.ZERO;
                        }

                    } else if (String.class.equals(declaredField.getType())) {
                        value = null == parameterValue || null == parameterValue[0] ? "" : parameterValue[0];
                    }
                    if (null != value) {
                        declaredField.setAccessible(true);
                        declaredField.set(object, value);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void loadPaymentOptionPage(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            boolean isEngDocUpload = null != request.getParameter("DOC_UPLOAD") && !request.getParameter("DOC_UPLOAD").isEmpty() && Boolean.parseBoolean(request.getParameter("DOC_UPLOAD"));
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            List<ClaimCalculationSheetMainDto> calculationSheetList = calculationSheetService.getPaymentOptionCalculationSheetList(claimNo);
            String advancedAmountPending = calculationSheetService.isPendingAdvancedPayment(calculationSheetList) == true ? AppConstant.YES : AppConstant.NO;
            String pendingPayment = calculationSheetService.isPendingPayment(calculationSheetList) == true ? AppConstant.YES : AppConstant.NO;
            request.setAttribute("calculationSheetList", calculationSheetList);
            request.setAttribute("isAdvancePaymentPending", advancedAmountPending);
            request.setAttribute("isPendingPayment", pendingPayment);
            request.setAttribute("claimHandlerDto", claimHandlerDto);
            request.setAttribute(AppConstant.DOC_UPLOAD, isEngDocUpload);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/paymentOption.jsp");
    }

    private void forwardToSparePartCordinator(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Forward To Spare Part Coordinator : CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.forwardToSparePartsCoordinator(calSheetId, claimNo, user);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void forwardToScrutinizingTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Forward To Bill checked Team : CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.forwardToScrutinizingTeam(calSheetId, claimNo, user);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void forwardToScrutinizingTeamByClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Forward To Bill checked Team By Claim Handler: CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.forwardToScrutinizingTeamByClaimHandler(calSheetId, claimNo, user);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void forwardToSpecialTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        boolean isCancelledPolicy = null != request.getParameter("IS_CANCELLED_POLICY") && Boolean.parseBoolean(request.getParameter("IS_CANCELLED_POLICY"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Forward To Special Team ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.forwardToSpecialTeam(calSheetId, claimNo, user, outStandingPremium, isCancelledPolicy);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(e.getMessage());
            printWriter(request, response, json);
        }
    }

    private void recallCalsheetforwardedtoSpTeamByClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.recallCalsheetforwardedtoSpTeamByClaimHandler(calSheetId, claimNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void forwardToMofa(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String forwardingUserId = request.getParameter("forwardingUserId");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Forward To MOFA Team ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.forwardToMofa(calSheetId, claimNo, forwardingUserId, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void getMofaApprovalUsers(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        try {
            List<UserDto> mofaApprovalUsers = calculationSheetService.getMofaApprovalUsers();
            List<ListBoxItem> userList = new ArrayList<>();
            for (UserDto userDto : mofaApprovalUsers) {
                ListBoxItem item = new ListBoxItem();
                item.setUserCode(userDto.getUserCode());
                item.setFullName(userDto.getFirstName() + " " + userDto.getLastName());
                userList.add(item);
            }
            json = gson.toJson(userList);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void forwardToMofaApproval(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String selectedUserId = request.getParameter("selectedUserId");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Forward To MOFA Approval ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> Selected User :" + selectedUserId + " -> Current User :" + user.getUserId());
        try {
            calculationSheetService.forwardToMofaApproval(calSheetId, claimNo, selectedUserId, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToClaimHandlerBySpc(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Return To Claim Handler By Spare Part Coordinator ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.returnToClaimHandlerBySpc(calSheetId, claimNo, user, false);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToClaimHandlerBySpecialTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Return To Claim Handler By Special Team ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.returnToClaimHandlerBySpecialTeam(calSheetId, claimNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToClaimHandlerAfterApproved(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String specialRemark = request.getParameter("specialRemark");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.returnToClaimHandlerBySpecialTeamAfterApproved(calSheetId, claimNo, specialRemark, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToClaimHandlerByMofa(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Return To Claim Handler By Mofa ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.returnToClaimHandlerByMofa(calSheetId, claimNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void recallByClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Recall by claim handler ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.recallByClaimHandler(calSheetId, claimNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (ErrorMsgException error) {
            LOGGER.error(error.getErrorMessage());
            json = gson.toJson(error.getErrorMessage());
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToClaimHandlerByStm(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Return by Scrutinizing Team Member ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());

        try {
            calculationSheetService.returnToClaimHandlerByStm(calSheetId, claimNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToSparePartCoordinatorByStm(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Return to Spare Part Coordinator by Scrutinizing Team Member ,  CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> User :" + user.getUserId());
        try {
            calculationSheetService.returnToSparePartCoordinatorByStm(calSheetId, claimNo, user);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void approvePayment(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        boolean isCancelledPolicy = null != request.getParameter("IS_CANCELLED_POLICY") && Boolean.parseBoolean(request.getParameter("IS_CANCELLED_POLICY"));

        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Approved : CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> Approve User :" + user.getUserId());
        try {
            calculationSheetService.approvePayment(calSheetId, claimNo, user, outStandingPremium, isCancelledPolicy);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (ErrorMsgException ex) {
            if (ex.getField().equalsIgnoreCase(AppConstant.PAYMENT_APPROVE_ERROR_MSG)) {
                json = gson.toJson("APPROVE_ERROR");
            } else if (AppConstant.EXCESS_RESERVE_ERROR_MSG.equals(ex.getField())) {
                json = gson.toJson("EXCESS_RESERVE");
            } else {
                json = gson.toJson("PAYABLE_AMOUNT_ERROR");
            }
            printWriter(request, response, json);
        } catch (WrongValueException e) {
            json = gson.toJson("ADVANCE_AMOUNT_ERROR");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(e.getMessage());
            printWriter(request, response, json);
        }
    }

    private void callPremiumOutstanding(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String email = request.getParameter("email") == null ? AppConstant.STRING_EMPTY : request.getParameter("email");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.callPremiumOutstanding(calSheetId, claimNo, email, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void callNoObjection(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String email = request.getParameter("email") == null ? AppConstant.STRING_EMPTY : request.getParameter("email");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.callNoObjection(calSheetId, claimNo, email, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void revokeNoObjection(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String email = request.getParameter("email") == null ? AppConstant.STRING_EMPTY : request.getParameter("email");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.revokeNoObjection(calSheetId, claimNo, email, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void revokePremiumOutstanding(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String email = request.getParameter("email") == null ? AppConstant.STRING_EMPTY : request.getParameter("email");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.revokePremiumOutstanding(calSheetId, claimNo, email, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void rejectPayment(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            calculationSheetService.rejectPayment(calSheetId, claimNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void generateVoucher(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        boolean isCancelledPolicy = null != request.getParameter("IS_CANCELLED_POLICY") && Boolean.parseBoolean(request.getParameter("IS_CANCELLED_POLICY"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        LOGGER.info("Payment Voucher Generate : CalSheet No : " + calSheetId + " -> Claim No : " + claimNo + " -> Voucher Generate User :" + user.getUserId());
        try {
            calculationSheetService.generateVoucher(calSheetId, claimNo, outStandingPremium, isCancelledPolicy, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (ErrorMsgException ex) {
            if (AppConstant.EXCESS_RESERVE_ERROR_MSG.equals(ex.getField())) {
                json = gson.toJson("EXCESS_RESERVE");
            } else {
                json = gson.toJson("PAYABLE_AMOUNT_ERROR");
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void calList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String calStatus = request.getParameter(AppConstant.TXT_CAL_SHEET_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CAL_SHEET_STATUS);
        String noObjection = request.getParameter(AppConstant.TXT_CALL_NO_OBJECTION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CALL_NO_OBJECTION);
        String noObjectionLetterAttached = request.getParameter(AppConstant.TXT_CALL_NO_OBJECTION_LETTER_ATTACHED) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CALL_NO_OBJECTION_LETTER_ATTACHED);
        String premiumOutstanding = request.getParameter(AppConstant.TXT_PREMIUM_OUTSTANDING) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_PREMIUM_OUTSTANDING);
        String premiumOutstandingLetterAttached = request.getParameter(AppConstant.TXT_PREMIUM_OUTSTANDING_LETTER_ATTACHED) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_PREMIUM_OUTSTANDING_LETTER_ATTACHED);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        String claimUser = request.getParameter(AppConstant.TXT_ASSIGN__USER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSIGN__USER);
        HttpSession session = request.getSession();
        boolean isSparePartsCoordinator = (boolean) session.getAttribute(AppConstant.IS_SPARE_COORDINATOR);
        boolean isScrutinizingTeam = (boolean) session.getAttribute(AppConstant.IS_SCRUTINIZING_COORDINATOR);
        boolean isSpecialTeam = (boolean) session.getAttribute(AppConstant.IS_SPECIAL_TEAM) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_SPECIAL_TEAM);
        boolean isMofaTeam = (boolean) session.getAttribute(AppConstant.IS_MOFA_TEAM) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_MOFA_TEAM);
        boolean isClaimHandler = (boolean) session.getAttribute(AppConstant.IS_CLAIM_HANDLER_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER);
        String userId = getSessionUser(request).getUserId();
        UserDto user = getSessionUser(request);

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t3.V_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }
            if (!"All".equals(calStatus)) {
                this.addFieldParameter("t3.V_STATUS", calStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (!"All".equals(noObjection) && 50 != type) {
                this.addFieldParameter("t3.V_NO_OBJECTION_STATUS", noObjection, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (!"All".equals(noObjectionLetterAttached) && 50 != type) {
                if (noObjectionLetterAttached.equalsIgnoreCase(AppConstant.YES)) {
                    this.addFieldParameter("t3.V_NO_OBJECTION_STATUS", "C", FieldParameterDto.SearchType.Equal, parameterList);
                    this.addFieldParameter("t3.V_IS_NO_OBJECTION_UPLOAD", "U", FieldParameterDto.SearchType.Equal, parameterList);
                } else {
                    this.addFieldParameter("t3.V_IS_NO_OBJECTION_UPLOAD", "U", FieldParameterDto.SearchType.NOT_Equal, parameterList);
                }
            }
            if (!"All".equals(premiumOutstanding) && 50 != type) {
                this.addFieldParameter("t3.V_PREMIUM_OUTSTANDING_STATUS", premiumOutstanding, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (!"All".equals(premiumOutstandingLetterAttached) && 50 != type) {
                if (premiumOutstandingLetterAttached.equalsIgnoreCase(AppConstant.YES)) {
                    this.addFieldParameter("t3.V_PREMIUM_OUTSTANDING_STATUS", "C", FieldParameterDto.SearchType.Equal, parameterList);
                    this.addFieldParameter("t3.V_IS_PREMIUM_OUTSTANDING_UPLOAD", "U", FieldParameterDto.SearchType.Equal, parameterList);
                } else {
                    this.addFieldParameter("t3.V_IS_PREMIUM_OUTSTANDING_UPLOAD", "U", FieldParameterDto.SearchType.NOT_Equal, parameterList);
                }
            }

            if (isSparePartsCoordinator) {
                this.addFieldParameter("t3.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            } else if (isScrutinizingTeam) {
                this.addFieldParameter("t3.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            } else if (isMofaTeam) {
                this.addFieldParameter("t3.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            } else if (isSpecialTeam) {
                this.addFieldParameter("t3.V_SPECIAL_TEAM_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            } else {
                // this.addFieldParameter("t3.V_INPUT_USER", userId, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if ("".equals(finalizedStatus)) {
                // this.addFieldParameter("t2.V_CLOSE_STATUS", "'REOPEN','CLOSE'", FieldParameterDto.SearchType.NOT_IN, parameterList);
            } else {
                this.addFieldParameter("t2.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

//
//            if (2 == type && decisionMakerUser) {
//                this.addFieldParameter("t2.V_DECISION_MAKING_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
//            } else
//            if (!"".equals(claimUser)) {
//                this.addFieldParameter("t2.V_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
//            }
            switch (type) {
                case 7:
                case 70:
                    if (isSparePartsCoordinator) {
                        this.addFieldParameter("t3.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                    }
                    break;
                case 6:
                case 60:
                    if (isScrutinizingTeam) {
                        this.addFieldParameter("t3.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                    } else if (isClaimHandler) {
                        this.addFieldParameter("t2.V_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                    }
                    break;
                case 8:
                case 9:
                    if (isClaimHandler) {
                        this.addFieldParameter("t2.V_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                    } else if (isSpecialTeam) {
                        this.addFieldParameter("t3.V_SPECIAL_TEAM_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                    } else if (isMofaTeam) {
                        this.addFieldParameter("t3.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                    } else if
                    (!"".equals(claimUser)) {
                        this.addFieldParameter("t2.V_ASSIGN_USER_ID", claimUser, FieldParameterDto.SearchType.Equal, parameterList);
                    }
                    break;
                case 27:
                case 28:
                    if (isSpecialTeam) {
                        this.addFieldParameter("t3.V_SPECIAL_TEAM_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                    }
                    break;
                case 50:
                    if (1 != user.getAccessUserType()) {
                        this.addFieldParameter("t3.V_RTE_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                    }
                    break;
            }
            orderColumnName = switch (orderColumnName) {
                case "txnId" -> "t2.N_TXN_NO";
                case "claimNo" -> "t1.N_CLIM_NO";
                case "policyNumberValue" -> "t1.V_POL_NUMBER";
                case "vehicleNo" -> "t1.V_VEHICLE_NO";
                case "callUser" -> "t1.V_CALL_USER";
                case "dateOfReport" -> "t1.D_DATE_OF_REPORT";
                case "timeOfReport" -> "t1.T_TIME_OF_REPORT";
                case "coverNoteNo" -> "t1.V_COVER_NOTE_NO";
                case "reporterName" -> "t1.V_REPORTER_NAME";
                case "assignDateTime" -> "t1.D_ACCID_DATE";
                case "accidTime" -> "t1.T_ACCID_TIME";
                case "acr" -> "t2.N_APRV_TOT_ACR_AMOUNT";
                case "claimStatusDesc" -> "t3.v_status_desc";
                case "presentReverseAmount" -> "t2.N_RESERVE_AMOUNT";
                case "liabilityAssignUser" -> "t2.V_LIABILITY_APRV_ASSIGN_USER";
                case "liabilityAssignDatetime" -> "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                case "intLiabilityAssignUser" -> "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                case "intLiabilityAssignDatetime" -> "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                case "calSheetStatus" -> "t4.V_STATUS";
                case "noObjection" -> "t3.V_NO_OBJECTION_STATUS";
                case "premiumOutstanding" -> "t3.V_PREMIUM_OUTSTANDING_STATUS";
                case "calSheetNo" -> "t3.N_CAL_SHEET_ID";
                case "calSheetInpUserId" -> "t3.V_INPUT_USER";
                case "rteAssignUserId" -> "t3.V_RTE_ASSIGN_USER_ID";
                case "rteAssignDateTime" -> "t3.D_RTE_ASSIGN_DATE_TIME";
                default -> orderColumnName;
            };
            DataGridDto data = calculationSheetService.getCalculationSheetDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, type);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void claimCalList(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        UserDto user = getSessionUser(request);
        ClaimUserTypeDto claimUserTypeDto = claimUserTypeDto(request);


        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,50,5,84)");
        List<PopupItemDto> popupItemCalDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(59,60,61,63,64,65,66,67,70,71)");
        List<PopupItemDto> popupAssignUserDtoList = new ArrayList<>();
        int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));

        if (claimUserTypeDto.isClaimHandlerUser()) {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrid='" + user.getUserId() + "'");
        } else if (claimUserTypeDto.isOfferTeamClaimHandlerUser()) {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrid='" + user.getUserId() + "'");
        } else if (claimUserTypeDto.isScrutinizingTeam()) {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrid='" + user.getUserId() + "'");
        } else if (claimUserTypeDto.isSparePartsCoordinator()) {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrid='" + user.getUserId() + "'");
        } else if (claimUserTypeDto.isSpecialTeam()) {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrid='" + user.getUserId() + "'");
        } else if (claimUserTypeDto.isOfferTeamSpecialTeam()) {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrid='" + user.getUserId() + "'");
        } else if (claimUserTypeDto.isMofaTeam()) {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrid='" + user.getUserId() + "'");
        } else if (claimUserTypeDto.isOfferTeamMofaTeam()) {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrid='" + user.getUserId() + "'");
        } else {
            popupAssignUserDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "n_accessusrtype IN(41,61)");
            popupAssignUserDtoList.add(0, new PopupItemDto("", "All"));
        }


        removeSessionType(request, response);
        updateSessionType(request, response, type);
        removeSessionClaimDetails(request, response);
        request.setAttribute("statusList", popupItemDtoList);
        request.setAttribute("calStatusList", popupItemCalDtoList);
        request.setAttribute("assignUserList", popupAssignUserDtoList);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calculationSheetList.jsp");
    }

    private void calculate(HttpServletRequest request, HttpServletResponse response) {
        String calType = request.getParameter("CAL_TYPE");
        try {
            BigDecimal totalFee;
            if ("AmountPercentage".equals(calType)) {
                String amount = getCalculableAmount(request.getParameter("amount"));
                String rate = getCalculableAmount(request.getParameter("rate"));
                totalFee = new BigDecimal(amount).multiply(new BigDecimal(rate)).divide(new BigDecimal(100));
            } else if ("RemoveVatAmount".equals(calType)) {
                String amount = getCalculableAmount(request.getParameter("amount"));
                String rate = getCalculableAmount(request.getParameter("rate"));
                totalFee = new BigDecimal(amount).multiply(new BigDecimal(rate)).divide(new BigDecimal(100).add(new BigDecimal(rate)), RoundingMode.HALF_UP);
            } else {
                totalFee = BigDecimal.ZERO;
            }

            try {
                BigDecimal scaled = totalFee.setScale(2, RoundingMode.HALF_UP);
                response.getWriter().println(scaled.toString());
            } catch (IOException ex) {
                LOGGER.error(ex.getMessage());;
            }

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    private String getCalculableAmount(String parameter) {
        if (null != parameter && !parameter.isEmpty()) {
            return parameter.replaceAll(",", "");
        } else {
            return "0";
        }
    }

    private void getMofaUserList(HttpServletRequest request, HttpServletResponse response) {
        try {
            Gson gson = new Gson();
            String amount = request.getParameter("amount");
            Integer claimNo = Integer.parseInt(null == request.getParameter("claimNo") ? AppConstant.ZERO : request.getParameter("claimNo"));
            List<UserDto> list = calculationSheetService.getMofaUserList(amount, claimNo);
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    private void loadBtnPanel(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        try {
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            Integer calSheetId = request.getParameter("calSheetId") == null || request.getParameter("calSheetId").isEmpty() ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto;
            if (calSheetId == 0) {
                //  claimCalculationSheetMainDto = calculationSheetService.getCalculationSheet(calSheetId);
                claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
            } else {
                claimCalculationSheetMainDto = calculationSheetService.getClaimCalculationSheetMainDto(claimNo, calSheetId, user);
            }

            request.setAttribute("claimCalculationSheetMainDto", claimCalculationSheetMainDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calculationSheetBtnPanel.jsp");
    }

    private void loadBtnPanelNew(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        boolean pendingInspection = false;
        boolean isEngDocUpload = null != request.getParameter("DOC_UPLOAD") && !request.getParameter("DOC_UPLOAD").isEmpty() && Boolean.parseBoolean(request.getParameter("DOC_UPLOAD"));
        try {
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            Integer calSheetId = request.getParameter("calSheetId") == null || request.getParameter("calSheetId").isEmpty() ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto;
            if (calSheetId == 0) {
                claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
                claimCalculationSheetMainDto.setAssignUserId(user.getUserId());
                claimCalculationSheetMainDto.setStatus(0);
            } else {
                claimCalculationSheetMainDto = calculationSheetService.getClaimCalculationSheetMainDto(claimNo, calSheetId, user);
            }

            pendingInspection = motorEngineerService.checkPendingInspection(claimNo);

            request.setAttribute("claimCalculationSheetMainDto", claimCalculationSheetMainDto);
            request.setAttribute(AppConstant.PENDING_INSPECTION, pendingInspection);
            request.setAttribute(AppConstant.DOC_UPLOAD, isEngDocUpload);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/calculationSheet/calculationSheetButtonPanel.jsp");
    }

    private void saveSpecialRemark(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetId = request.getParameter("calSheetId") == null || request.getParameter("calSheetId").isEmpty() ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        String remark = request.getParameter("remark");
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        String json;

        try {
            calculationSheetService.saveSpecialRemark(calSheetId, remark, user);
            calculationSheetService.saveClaimSpecialRemarkByCalsheetId(calSheetId, remark, user);
            json = gson.toJson("Success");
            printWriter(request, response, json);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getSupplierDetails(HttpServletRequest request, HttpServletResponse response) {
        Integer supplierId = request.getParameter("supplierId") == null ? 0 : Integer.parseInt(request.getParameter("supplierId"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            SupplyOrderSummaryDto supplierDetails = calculationSheetService.getSupplierDetails(supplierId);
            json = gson.toJson(supplierDetails);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());

        }
    }

    private void calculateReplacement(HttpServletRequest request, HttpServletResponse response) {
        BigDecimal approvedTotalAmount = BigDecimal.ZERO;
        BigDecimal nbtTotalAmount = BigDecimal.ZERO;
        BigDecimal vatTotalAmount = BigDecimal.ZERO;
        BigDecimal oaTotalAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;

        BigDecimal totalAmountWithoutVat = BigDecimal.ZERO;
        BigDecimal totalAmountAfterOa = BigDecimal.ZERO;
        BigDecimal totalAmountWithNbt = BigDecimal.ZERO;

        CalculationSheetFormDto calculationSheetFormReplacement = new CalculationSheetFormDto();
        String json;
        Gson gson = new Gson();
        Map<String, String[]> parameterMap = request.getParameterMap();
        Enumeration<String> parameterNames = request.getParameterNames();
        List<String> processedRepalcementIndexList = new ArrayList<>();
        List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos = new ArrayList<>();
        try {
            while (parameterNames.hasMoreElements()) {
                String parameterName = parameterNames.nextElement();
                if (parameterName.contains("claimCalculationSheetDetailReplacementDtos")) {
                    String[] parameterNameArr = parameterName.split("\\.");
                    String parameterNamePrefix = parameterNameArr[0];
                    String parameterNameIndex = parameterNamePrefix.replaceAll("claimCalculationSheetDetailReplacementDtos\\[", "").replaceAll("\\]", "");
                    if (!processedRepalcementIndexList.contains(parameterNameIndex)) {
                        ClaimCalculationSheetDetailDto claimCalculationSheetDetailRepalcementDto = new ClaimCalculationSheetDetailDto();
                        populateDtoFromMap(claimCalculationSheetDetailRepalcementDto, parameterMap, parameterNamePrefix);
                        processedRepalcementIndexList.add(parameterNameIndex);

                        this.calculateAmountWithoutVat(claimCalculationSheetDetailRepalcementDto);
                        this.calculateOaAmount(claimCalculationSheetDetailRepalcementDto);
                        this.calculateTotalAmountAfterOa(claimCalculationSheetDetailRepalcementDto);
                        this.calculateNbtAmount(claimCalculationSheetDetailRepalcementDto);
                        this.calculateAmountWithNbt(claimCalculationSheetDetailRepalcementDto);
                        this.calculateVatAmount(claimCalculationSheetDetailRepalcementDto);
                        this.calculateTotalAmount(claimCalculationSheetDetailRepalcementDto);

                        claimCalculationSheetDetailReplacementDtos.add(claimCalculationSheetDetailRepalcementDto);

                        approvedTotalAmount = approvedTotalAmount.add(claimCalculationSheetDetailRepalcementDto.getApprovedAmount());
                        nbtTotalAmount = nbtTotalAmount.add(claimCalculationSheetDetailRepalcementDto.getNbtAmount());
                        vatTotalAmount = vatTotalAmount.add(claimCalculationSheetDetailRepalcementDto.getVatAmount());
                        oaTotalAmount = oaTotalAmount.add(claimCalculationSheetDetailRepalcementDto.getOaAmount());
                        totalAmount = totalAmount.add(claimCalculationSheetDetailRepalcementDto.getTotalAmount());

                        totalAmountWithoutVat = totalAmountWithoutVat.add(claimCalculationSheetDetailRepalcementDto.getAmountWithoutVat());
                        totalAmountAfterOa = totalAmountAfterOa.add(claimCalculationSheetDetailRepalcementDto.getTotalAmountAfterOa());
                        totalAmountWithNbt = totalAmountWithNbt.add(claimCalculationSheetDetailRepalcementDto.getAmountWithNbt());
                    }
                }

            }
            calculationSheetFormReplacement.setApprovedTotalAmount(approvedTotalAmount);
            calculationSheetFormReplacement.setNbtTotalAmount(nbtTotalAmount);
            calculationSheetFormReplacement.setVatTotalAmount(vatTotalAmount);
            calculationSheetFormReplacement.setOaTotalAmount(oaTotalAmount);
            calculationSheetFormReplacement.setTotalAmount(totalAmount);

            calculationSheetFormReplacement.setTotalAmountWithoutVat(totalAmountWithoutVat);
            calculationSheetFormReplacement.setTotalAmountAfterOa(totalAmountAfterOa);
            calculationSheetFormReplacement.setTotalAmountWithNbt(totalAmountWithNbt);

            calculationSheetFormReplacement.setClaimCalculationSheetDetailReplacementDtos(claimCalculationSheetDetailReplacementDtos);
            json = gson.toJson(calculationSheetFormReplacement);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void calculateLabour(HttpServletRequest request, HttpServletResponse response) {
        BigDecimal approvedTotalAmount = BigDecimal.ZERO;
        BigDecimal nbtTotalAmount = BigDecimal.ZERO;
        BigDecimal vatTotalAmount = BigDecimal.ZERO;
        BigDecimal oaTotalAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;

        BigDecimal totalAmountWithoutVat = BigDecimal.ZERO;
        BigDecimal totalAmountAfterOa = BigDecimal.ZERO;
        BigDecimal totalAmountWithNbt = BigDecimal.ZERO;

        CalculationSheetFormDto calculationSheetFormLabour = new CalculationSheetFormDto();
        String json;
        Gson gson = new Gson();
        Map<String, String[]> parameterMap = request.getParameterMap();
        Enumeration<String> parameterNames = request.getParameterNames();
        List<String> processedLabourIndexList = new ArrayList<>();
        List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos = new ArrayList<>();
        try {
            while (parameterNames.hasMoreElements()) {
                String parameterName = parameterNames.nextElement();

                if (parameterName.contains("claimCalculationSheetDetailLabourDtos")) {
                    String[] parameterNameArr = parameterName.split("\\.");
                    String parameterNamePrefix = parameterNameArr[0];
                    String parameterNameIndex = parameterNamePrefix.replaceAll("claimCalculationSheetDetailLabourDtos\\[", "").replaceAll("\\]", "");
                    if (!processedLabourIndexList.contains(parameterNameIndex)) {
                        ClaimCalculationSheetDetailDto claimCalculationSheetDetailLabourDto = new ClaimCalculationSheetDetailDto();
                        populateDtoFromMap(claimCalculationSheetDetailLabourDto, parameterMap, parameterNamePrefix);
                        processedLabourIndexList.add(parameterNameIndex);

                        this.calculateAmountWithoutVat(claimCalculationSheetDetailLabourDto);
                        this.calculateOaAmount(claimCalculationSheetDetailLabourDto);
                        this.calculateTotalAmountAfterOa(claimCalculationSheetDetailLabourDto);
                        this.calculateNbtAmount(claimCalculationSheetDetailLabourDto);
                        this.calculateAmountWithNbt(claimCalculationSheetDetailLabourDto);
                        this.calculateVatAmount(claimCalculationSheetDetailLabourDto);
                        this.calculateTotalAmount(claimCalculationSheetDetailLabourDto);

                        claimCalculationSheetDetailLabourDtos.add(claimCalculationSheetDetailLabourDto);

                        approvedTotalAmount = approvedTotalAmount.add(claimCalculationSheetDetailLabourDto.getApprovedAmount());
                        nbtTotalAmount = nbtTotalAmount.add(claimCalculationSheetDetailLabourDto.getNbtAmount());
                        vatTotalAmount = vatTotalAmount.add(claimCalculationSheetDetailLabourDto.getVatAmount());
                        oaTotalAmount = oaTotalAmount.add(claimCalculationSheetDetailLabourDto.getOaAmount());
                        totalAmount = totalAmount.add(claimCalculationSheetDetailLabourDto.getTotalAmount());

                        totalAmountWithoutVat = totalAmountWithoutVat.add(claimCalculationSheetDetailLabourDto.getAmountWithoutVat());
                        totalAmountAfterOa = totalAmountAfterOa.add(claimCalculationSheetDetailLabourDto.getTotalAmountAfterOa());
                        totalAmountWithNbt = totalAmountWithNbt.add(claimCalculationSheetDetailLabourDto.getAmountWithNbt());
                    }
                }
            }
            calculationSheetFormLabour.setApprovedTotalAmount(approvedTotalAmount);
            calculationSheetFormLabour.setNbtTotalAmount(nbtTotalAmount);
            calculationSheetFormLabour.setVatTotalAmount(vatTotalAmount);
            calculationSheetFormLabour.setOaTotalAmount(oaTotalAmount);
            calculationSheetFormLabour.setTotalAmount(totalAmount);

            calculationSheetFormLabour.setTotalAmountWithoutVat(totalAmountWithoutVat);
            calculationSheetFormLabour.setTotalAmountAfterOa(totalAmountAfterOa);
            calculationSheetFormLabour.setTotalAmountWithNbt(totalAmountWithNbt);

            calculationSheetFormLabour.setClaimCalculationSheetDetailLabourDtos(claimCalculationSheetDetailLabourDtos);
            json = gson.toJson(calculationSheetFormLabour);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void calculateAmountWithNbt(ClaimCalculationSheetDetailDto dto) {
        dto.setAmountWithNbt(dto.getTotalAmountAfterOa().add(dto.getNbtAmount()).setScale(2, RoundingMode.HALF_UP));
    }

    private void calculateTotalAmountAfterOa(ClaimCalculationSheetDetailDto dto) {
        dto.setTotalAmountAfterOa(dto.getAmountWithoutVat().subtract(dto.getOaAmount().setScale(2, RoundingMode.HALF_UP)));
    }

    private void calculateAmountWithoutVat(ClaimCalculationSheetDetailDto dto) {
        BigDecimal amountWithoutVat = new BigDecimal("0.00");
        switch (dto.getIsRemoveVat()) {
            case AppConstant.YES:
                amountWithoutVat = ((dto.getApprovedAmount().setScale(2, RoundingMode.HALF_UP).multiply(new BigDecimal(100))).divide(dto.getRemoveVatRate().add(new BigDecimal(100)), RoundingMode.HALF_UP));
                break;
            default:
                amountWithoutVat = dto.getApprovedAmount();
                break;
        }
        dto.setAmountWithoutVat(amountWithoutVat.setScale(2, RoundingMode.HALF_UP));
    }

    private void calculateOaAmount(ClaimCalculationSheetDetailDto dto) {
        BigDecimal oaAmount = new BigDecimal(0.00);
        oaAmount = (dto.getAmountWithoutVat()).multiply(dto.getOa()).divide(new BigDecimal(100), RoundingMode.HALF_UP);
        dto.setOaAmount(oaAmount.setScale(2, RoundingMode.HALF_UP));
    }

    private void calculateNbtAmount(ClaimCalculationSheetDetailDto dto) {
        BigDecimal nbtAmount = new BigDecimal("0.00");
        switch (dto.getIsAddNbt()) {
            case AppConstant.YES:
                nbtAmount = ((dto.getTotalAmountAfterOa().multiply(dto.getNbtRate())).divide(new BigDecimal(100), RoundingMode.HALF_UP));
                break;
        }
        dto.setNbtAmount(nbtAmount.setScale(2, RoundingMode.HALF_UP));
    }

    private void calculateVatAmount(ClaimCalculationSheetDetailDto dto) {
        BigDecimal vatAmount = new BigDecimal("0.00");

        switch (dto.getAddVatType()) {
            case AppConstant.ADD_VAT_WITHOUT_NBT:
                vatAmount = ((dto.getTotalAmountAfterOa().multiply(dto.getVatRate())).divide(new BigDecimal(100), RoundingMode.HALF_UP));
                break;
            case AppConstant.ADD_VAT_WITH_NBT:
                vatAmount = ((dto.getAmountWithNbt().multiply(dto.getVatRate())).divide(new BigDecimal(100), RoundingMode.HALF_UP));
                break;
        }
        dto.setVatAmount(vatAmount.setScale(2, RoundingMode.HALF_UP));
    }

    private void calculateTotalAmount(ClaimCalculationSheetDetailDto dto) {
        dto.setTotalAmount(dto.getAmountWithNbt().add(dto.getVatAmount()).setScale(2, RoundingMode.HALF_UP));
    }

    private void loadPayeeList(HttpServletRequest request, HttpServletResponse response) {
//        ClaimHandlerDto claimHandlerDto = (ClaimHandlerDto) request.getAttribute("claimHandlerDto");
//        if (claimHandlerDto == null) {
//            claimHandlerDto = (ClaimHandlerDto) request.getSession().getAttribute("superDashboardClaimHandlerDtoSession");
//        }
        String customerName = null == request.getParameter("custName") ? AppConstant.STRING_EMPTY : request.getParameter("custName");
        Integer payeeType = Integer.parseInt(request.getParameter("payeeType") == null ? "0" : request.getParameter("payeeType"));
        Integer supplierOrderId = Integer.parseInt(request.getParameter("supplierOrderId") == null ? "0" : request.getParameter("supplierOrderId"));
        Boolean isReadonly = Boolean.parseBoolean(request.getParameter("isReadonly") == null ? "false" : request.getParameter("isReadonly"));
        String selectPayeeDesc = request.getParameter("selectPayeeDesc") == null ? "" : request.getParameter("selectPayeeDesc");
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        Gson gson = new Gson();
        List<ListBoxItem> list = new ArrayList<>();
        try {

            //   ListBoxItem item = new ListBoxItem("0", "Please select one");
            list = calculationSheetService.getPayeeSelectItem(payeeType, supplierOrderId, customerName, isReadonly, selectPayeeDesc, claimNo);
            // list.add(item);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            json = gson.toJson(list);
            printWriter(request, response, json);
        }
    }

    private void loadPayeeTypeList(HttpServletRequest request, HttpServletResponse response) {
        Integer calSheetType = Integer.parseInt(request.getParameter("calSheetType") == null ? "0" : request.getParameter("calSheetType"));
        Boolean isReadonly = Boolean.parseBoolean(request.getParameter("isReadonly") == null ? "false" : request.getParameter("isReadonly"));
        String selectPayeeType = request.getParameter("selectPayeeType") == null ? "" : request.getParameter("selectPayeeType");

        String json;
        Gson gson = new Gson();
        List<ListBoxItem> list = new ArrayList<>();
        try {
            list = calculationSheetService.getPayeeTypeSelectItem(calSheetType, isReadonly, selectPayeeType);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            json = gson.toJson(list);
            printWriter(request, response, json);
        }
    }
}
